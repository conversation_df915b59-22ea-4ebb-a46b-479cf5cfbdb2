package com.futures.system.source;

import com.futures.system.model.OrderBookEvent;
import org.apache.flink.api.common.eventtime.SerializableTimestampAssigner;
import org.apache.flink.api.common.eventtime.WatermarkStrategy;

import java.time.Duration;

/**
 * 订单簿事件水印策略
 * 为订单簿事件提供事件时间提取和水印生成
 */
public class OrderBookEventWatermarkStrategy {
    
    /**
     * 默认最大乱序时间：1秒
     */
    private static final Duration DEFAULT_MAX_OUT_OF_ORDERNESS = Duration.ofSeconds(1);
    
    /**
     * 默认空闲超时时间：5秒
     */
    private static final Duration DEFAULT_IDLE_TIMEOUT = Duration.ofSeconds(5);
    
    /**
     * 创建订单簿事件的水印策略
     */
    public static WatermarkStrategy<OrderBookEvent> create() {
        return create(DEFAULT_MAX_OUT_OF_ORDERNESS, DEFAULT_IDLE_TIMEOUT);
    }
    
    /**
     * 创建自定义参数的水印策略
     */
    public static WatermarkStrategy<OrderBookEvent> create(Duration maxOutOfOrderness, Duration idleTimeout) {
        return WatermarkStrategy
                .<OrderBookEvent>forBoundedOutOfOrderness(maxOutOfOrderness)
                .withTimestampAssigner(new OrderBookEventTimestampAssigner())
                .withIdleness(idleTimeout);
    }
    
    /**
     * 订单簿事件时间戳分配器
     */
    private static class OrderBookEventTimestampAssigner implements SerializableTimestampAssigner<OrderBookEvent> {
        
        @Override
        public long extractTimestamp(OrderBookEvent event, long recordTimestamp) {
            try {
                // 优先使用事件自身的时间戳
                long eventTimestamp = event.getEventTimestamp();
                
                // 如果事件时间戳无效，使用记录时间戳
                if (eventTimestamp <= 0) {
                    return recordTimestamp > 0 ? recordTimestamp : System.currentTimeMillis();
                }
                
                return eventTimestamp;
            } catch (Exception e) {
                // 异常情况下使用当前时间
                return System.currentTimeMillis();
            }
        }
    }
}
