# 期货订单簿重建和盈亏计算系统 - 输出配置

# ===========================================
# 输出方案配置
# ===========================================
# 输出方案类型: DEBUG_CONSOLE, PRODUCTION_CLICKHOUSE, HYBRID
output.solution.type=DEBUG_CONSOLE

# ===========================================
# 调试输出配置
# ===========================================
# 是否启用调试输出
debug.output.enabled=true

# 订单簿快照输出配置
debug.orderbook.enabled=true
debug.orderbook.interval.ms=1000

# 盈亏计算输出配置
debug.pnl.calculation.enabled=true
debug.pnl.calculation.interval.ms=2000

# 盈亏汇总输出配置
debug.pnl.summary.enabled=true
debug.pnl.summary.interval.ms=5000

# 日志级别
debug.log.level=INFO

# ===========================================
# ClickHouse生产输出配置
# ===========================================
# 是否启用ClickHouse输出
clickhouse.output.enabled=false

# ClickHouse连接配置
clickhouse.jdbc.url=****************************************
clickhouse.username=default
clickhouse.password=
clickhouse.database=default

# ClickHouse批量写入配置
clickhouse.batch.size=1000
clickhouse.flush.interval.ms=5000
clickhouse.max.retries=3
clickhouse.retry.delay.ms=1000
clickhouse.connection.pool.size=5

# ClickHouse连接超时配置
clickhouse.socket.timeout.ms=30000
clickhouse.connection.timeout.ms=10000

# ===========================================
# 环境特定配置
# ===========================================

# 开发环境配置
[development]
output.solution.type=DEBUG_CONSOLE
debug.output.enabled=true
debug.orderbook.interval.ms=500
debug.pnl.calculation.interval.ms=1000
debug.pnl.summary.interval.ms=2000
debug.log.level=DEBUG

# 测试环境配置
[testing]
output.solution.type=HYBRID
debug.output.enabled=true
debug.orderbook.enabled=false
debug.pnl.calculation.interval.ms=3000
debug.pnl.summary.interval.ms=10000
clickhouse.output.enabled=true
clickhouse.batch.size=500
clickhouse.flush.interval.ms=10000

# 生产环境配置
[production]
output.solution.type=PRODUCTION_CLICKHOUSE
debug.output.enabled=false
clickhouse.output.enabled=true
clickhouse.batch.size=2000
clickhouse.flush.interval.ms=3000
clickhouse.max.retries=5
clickhouse.retry.delay.ms=2000
clickhouse.connection.pool.size=10

# 高性能环境配置
[high-performance]
output.solution.type=PRODUCTION_CLICKHOUSE
clickhouse.batch.size=5000
clickhouse.flush.interval.ms=1000
clickhouse.connection.pool.size=15

# ===========================================
# 监控和告警配置
# ===========================================
# 输出监控配置
output.monitoring.enabled=true
output.monitoring.interval.ms=30000

# 性能指标配置
output.metrics.enabled=true
output.metrics.batch.success.threshold=0.95
output.metrics.latency.threshold.ms=1000

# 告警配置
output.alert.enabled=false
output.alert.email=<EMAIL>
output.alert.batch.failure.threshold=5
output.alert.connection.failure.threshold=3
